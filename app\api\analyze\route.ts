import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const image = formData.get('image') as File;
    const ticker = formData.get('ticker') as string;

    if (!image || !ticker) {
      return NextResponse.json(
        { error: 'Image and ticker are required' },
        { status: 400 }
      );
    }

    // Convert image to base64
    const bytes = await image.arrayBuffer();
    const base64 = Buffer.from(bytes).toString('base64');
    const imageUrl = `data:${image.type};base64,${base64}`;

    // System prompt for stock analysis
    const systemPrompt = `You're a professional stock market analyst with expertise in technical analysis.
Analyze the uploaded stock chart for ticker symbol: ${ticker}

Provide your analysis in the following structured format:

TREND: [Current trend - bullish/bearish/neutral with brief explanation]

SUPPORT_RESISTANCE: [Key support and resistance levels visible on the chart]

PATTERNS: [Any recognizable chart patterns like head & shoulders, triangles, flags, etc.]

RECOMMENDATION: [One clear, actionable trading recommendation]

Be concise, technical, and accurate. Focus on what you can actually see in the chart.`;

    // Call OpenAI API
    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: `Please analyze this stock chart for ${ticker}. Provide technical insights based on what you can see in the image.`,
            },
            {
              type: 'image_url',
              image_url: {
                url: imageUrl,
                detail: 'high',
              },
            },
          ],
        },
      ],
      max_tokens: 1000,
      temperature: 0.3,
    });

    const analysis = response.choices[0]?.message?.content || '';

    // Parse the structured response
    const trendMatch = analysis.match(/TREND:\s*(.*?)(?=\n\n|\nSUPPORT_RESISTANCE|$)/s);
    const supportResistanceMatch = analysis.match(/SUPPORT_RESISTANCE:\s*(.*?)(?=\n\n|\nPATTERNS|$)/s);
    const patternsMatch = analysis.match(/PATTERNS:\s*(.*?)(?=\n\n|\nRECOMMENDATION|$)/s);
    const recommendationMatch = analysis.match(/RECOMMENDATION:\s*(.*?)(?=\n\n|$)/s);

    const result = {
      trend: trendMatch?.[1]?.trim() || 'Analysis unavailable',
      supportResistance: supportResistanceMatch?.[1]?.trim() || 'No clear levels identified',
      patterns: patternsMatch?.[1]?.trim() || 'No significant patterns detected',
      recommendation: recommendationMatch?.[1]?.trim() || 'Further analysis required',
      fullAnalysis: analysis,
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Analysis error:', error);
    
    if (error instanceof Error && error.message.includes('API key')) {
      return NextResponse.json(
        { error: 'OpenAI API key is not configured' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to analyze chart. Please try again.' },
      { status: 500 }
    );
  }
}